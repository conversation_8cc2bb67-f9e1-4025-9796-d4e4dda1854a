/**
 * Custom Fields v2 Type Definitions
 * 
 * Comprehensive type definitions for the v2 custom field synchronization system.
 * Provides type safety and clear interfaces for all components.
 * 
 * @fileoverview Type definitions for custom fields v2 system
 * @version 2.0.0
 * @since 2024-08-07
 */

import type { APGetCustomFieldType, GetCCCustomField } from "@type";

// ============================================================================
// ENUMS
// ============================================================================

/**
 * Field matching strategies for intelligent field pairing
 */
export enum FieldMatchStrategy {
	/** Exact name/label matching */
	EXACT = "exact",
	/** Normalized matching with German chars, spaces, case handling */
	NORMALIZED = "normalized", 
	/** Fuzzy matching with similarity threshold */
	FUZZY = "fuzzy"
}

/**
 * Supported platforms for field synchronization
 */
export enum Platform {
	AP = "ap",
	CC = "cc"
}

/**
 * Field mapping types
 */
export enum MappingType {
	CUSTOM_TO_CUSTOM = "custom_to_custom",
	CUSTOM_TO_STANDARD = "custom_to_standard", 
	STANDARD_TO_CUSTOM = "standard_to_custom"
}

// ============================================================================
// CONFIGURATION INTERFACES
// ============================================================================

/**
 * Configuration for field matching behavior
 */
export interface FieldMatchConfig {
	/** Primary matching strategy to use */
	strategy: FieldMatchStrategy;
	/** Fuzzy matching threshold (0.0 - 1.0) */
	fuzzyThreshold?: number;
	/** Normalize German characters (ä→a, ö→o, ü→u, ß→ss) */
	normalizeGermanChars?: boolean;
	/** Ignore case differences */
	ignoreCase?: boolean;
	/** Ignore spaces and underscores */
	ignoreSpaces?: boolean;
}

/**
 * Synchronization options for field operations
 */
export interface SyncOptions {
	/** Request ID for correlation */
	requestId: string;
	/** Create missing fields if no match found */
	createMissingFields?: boolean;
	/** Include standard field mappings */
	includeStandardFields?: boolean;
	/** Sync direction */
	direction?: "ap_to_cc" | "cc_to_ap" | "bidirectional";
	/** Logging level */
	logLevel?: "DEBUG" | "INFO" | "WARN" | "ERROR";
	/** Dry run mode (no actual changes) */
	dryRun?: boolean;
}

// ============================================================================
// FIELD MAPPING INTERFACES
// ============================================================================

/**
 * Field mapping between AP and CC platforms
 */
export interface FieldMapping {
	/** Unique mapping identifier */
	id: string;
	/** AutoPatient field ID */
	apFieldId: string;
	/** CliniCore field ID */
	ccFieldId: string;
	/** AutoPatient field name */
	apFieldName: string;
	/** CliniCore field name/label */
	ccFieldName: string;
	/** AutoPatient field type */
	apFieldType: string;
	/** CliniCore field type */
	ccFieldType: string;
	/** Matching strategy used */
	matchStrategy: FieldMatchStrategy;
	/** Confidence score (0.0 - 1.0) */
	confidence: number;
	/** Is this a standard field mapping */
	isStandardField: boolean;
	/** Mapping type */
	mappingType?: MappingType;
	/** Is mapping active */
	isActive: boolean;
}

/**
 * Standard field mapping configuration
 *
 * Defines how standard fields from one platform should be mapped to
 * custom fields on another platform (e.g., AP email → CC custom field).
 */
export interface StandardFieldMapping {
	/** Unique mapping identifier */
	id: string;
	/** Source field path (e.g., "patient.email") */
	sourceField: string;
	/** Source platform */
	sourcePlatform: Platform;
	/** Target platform */
	targetPlatform: Platform;
	/** Target field type to create/match */
	targetFieldType: string;
	/** Possible target field names to match against */
	targetFieldNames: string[];
	/** Human-readable description */
	description: string;
	/** Priority for mapping (lower = higher priority) */
	priority: number;
	/** Whether to create the field if no match is found */
	createIfMissing: boolean;
	/** Type of value conversion needed */
	valueConversion: "textbox_list_object" | "multi_value_to_text" | "boolean_conversion" | "direct_mapping";
}

// ============================================================================
// SYNCHRONIZATION RESULT INTERFACES
// ============================================================================

/**
 * Result of field definition synchronization
 */
export interface FieldSyncResult {
	/** Overall success status */
	success: boolean;
	/** Successfully matched fields */
	matchedFields: FieldMapping[];
	/** Newly created fields */
	createdFields: FieldMapping[];
	/** Skipped fields (incompatible, etc.) */
	skippedFields: Array<{
		fieldName: string;
		reason: string;
		platform: Platform;
	}>;
	/** Failed field operations */
	failedFields: Array<{
		fieldName: string;
		error: string;
		platform: Platform;
	}>;
	/** Processing time in milliseconds */
	processingTimeMs: number;
	/** Warning messages */
	warnings: string[];
}

/**
 * Result of patient value synchronization
 */
export interface ValueSyncResult {
	/** Overall success status */
	success: boolean;
	/** Patient ID */
	patientId: string;
	/** Number of fields processed */
	processedFields: number;
	/** Number of successful updates */
	successfulUpdates: number;
	/** Number of skipped fields */
	skippedFields: number;
	/** Number of failed updates */
	failedUpdates: number;
	/** Processing time in milliseconds */
	processingTimeMs: number;
	/** Error messages */
	errors: string[];
	/** Warning messages */
	warnings: string[];
}

/**
 * Context for value synchronization operations
 */
export interface ValueSyncContext {
	/** Patient ID */
	patientId: string;
	/** AP contact data (optional) */
	apContact?: import("@type").GetAPContactType;
	/** CC patient data (optional) */
	ccPatient?: import("@type").GetCCPatientType;
	/** Sync direction */
	direction: "ap_to_cc" | "cc_to_ap" | "bidirectional";
	/** Request ID */
	requestId: string;
}

/**
 * Summary of synchronization operations
 */
export interface SyncSummary {
	/** Patient ID */
	patientId: string;
	/** Number of fields processed */
	processedFields: number;
	/** Number of successful updates */
	successfulUpdates: number;
	/** Number of skipped fields */
	skippedFields: number;
	/** Number of failed updates */
	failedUpdates: number;
	/** Processing time in milliseconds */
	processingTimeMs: number;
	/** Error messages */
	errors: string[];
	/** Warning messages */
	warnings: string[];
}

// ============================================================================
// FIELD COMPATIBILITY INTERFACES
// ============================================================================

/**
 * Field compatibility check result
 */
export interface CompatibilityResult {
	/** Are fields compatible */
	compatible: boolean;
	/** Confidence score */
	confidence: number;
	/** Reason for compatibility/incompatibility */
	reason: string;
	/** Required conversion steps */
	conversionSteps?: string[];
}

/**
 * Value conversion result
 */
export interface ConversionResult {
	/** Conversion success status */
	success: boolean;
	/** Converted value */
	convertedValue: any;
	/** Conversion error message (if failed) */
	error?: string;
	/** Conversion notes and warnings */
	notes: string[];
}

/**
 * Value conversion context
 *
 * Provides context information for value conversion operations including
 * source and target field information and platform details.
 */
export interface ValueConversionContext {
	/** Source field information */
	sourceField?: APGetCustomFieldType | GetCCCustomField;
	/** Target field information */
	targetField?: APGetCustomFieldType | GetCCCustomField;
	/** Source platform */
	sourcePlatform: Platform;
	/** Target platform */
	targetPlatform: Platform;
	/** Additional conversion options */
	options?: {
		/** Preserve empty values */
		preserveEmpty?: boolean;
		/** Custom separator for multi-value conversion */
		customSeparator?: string;
		/** Force specific conversion type */
		forceConversionType?: string;
	};
}

// ============================================================================
// FIELD TYPE CONVERSION INTERFACES
// ============================================================================

/**
 * Field type conversion result
 *
 * Contains the target field type and metadata about the conversion process.
 * Used by the field type converter to communicate conversion decisions.
 */
export interface ConvertedFieldType {
	/** Target platform field type */
	targetType: string;
	/** Whether the field supports multiple values */
	isMultiValue: boolean;
	/** Fallback field type if primary type is not available */
	fallbackType?: string;
	/** Notes about the conversion process for debugging */
	conversionNotes: string[];
	/** Whether values need special conversion logic */
	requiresValueConversion: boolean;
	/** Type of value conversion needed */
	valueConversionType?: "textbox_list_object" | "multi_value_to_text" | "boolean_conversion" | "direct_mapping";
}

/**
 * Field matching result
 *
 * Represents a potential field match with confidence scoring.
 * Used by the field matcher to return ranked matching candidates.
 */
export interface FieldMatch {
	/** The matched field object */
	field: APGetCustomFieldType | GetCCCustomField;
	/** Confidence score (0.0 - 1.0) */
	confidence: number;
	/** Matching strategy that found this match */
	matchStrategy: FieldMatchStrategy;
	/** Which field property was matched on */
	matchedOn: "name" | "label" | "fieldKey";
	/** Field type of the matched field */
	fieldType: string;
}

/**
 * Field matching options
 *
 * Configuration options for field matching behavior.
 * Allows fine-tuning of the matching process.
 */
export interface FieldMatchOptions {
	/** Only match fields of this specific type */
	targetFieldType?: string;
	/** Exclude fields with these IDs from matching */
	excludeFieldIds?: string[];
	/** Minimum confidence score to include in results */
	minConfidence?: number;
	/** Maximum number of results to return */
	maxResults?: number;
}

// ============================================================================
// PATIENT DATA INTERFACES
// ============================================================================

/**
 * Patient data for synchronization
 */
export interface PatientData {
	/** Patient ID */
	id: string;
	/** AP contact data (optional) */
	apContact?: import("@type").GetAPContactType;
	/** CC patient data (optional) */
	ccPatient?: import("@type").GetCCPatientType;
	/** Platform this data comes from */
	platform: Platform;
	/** Standard fields */
	standardFields: Record<string, unknown>;
	/** Custom fields */
	customFields: Record<string, unknown>;
	/** Raw platform-specific data */
	rawData: unknown;
}

// ============================================================================
// EXPORT ALL TYPES
// ============================================================================

export type {
	APGetCustomFieldType,
	GetCCCustomField
} from "@type";
