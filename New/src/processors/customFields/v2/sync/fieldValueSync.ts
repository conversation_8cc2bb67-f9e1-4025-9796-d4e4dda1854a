/**
 * Field Value Sync Engine for Custom Fields v2
 *
 * Handles patient field value synchronization between AutoPatient (AP) and 
 * CliniCore (CC) platforms. This engine manages the conversion and transfer
 * of custom field values for individual patients.
 *
 * **Key Features:**
 * - Patient-specific field value synchronization
 * - Value conversion based on field type mappings
 * - TEXTBOX_LIST Record<string, string> handling
 * - Multi-value field conversion with proper separators
 * - Boolean field conversion (true→"Yes", false→"No")
 * - Medical field value handling
 * - Comprehensive error handling and validation
 *
 * **Process Flow:**
 * 1. Load field mappings for the patient's fields
 * 2. Extract current field values from both platforms
 * 3. Convert values based on field type mappings
 * 4. Update target platform with converted values
 * 5. Handle TEXTBOX_LIST option management
 * 6. Log synchronization results and errors
 *
 * @fileoverview Field value synchronization engine
 * @version 1.0.0
 * @since 2024-08-07
 */

import type { 
	APGetCustomFieldType, 
	GetCCCustomField,
	GetAPContactType,
	GetCCPatientType,
	PostCCPatientType,
	APPostContactType
} from "@type";
import type {
	ValueSyncResult,
	ValueSyncContext,
	SyncSummary,
	Platform
} from "../types";
import { convertFieldValue } from "../core/valueConverter";
import { convertFieldType } from "../core/fieldTypeConverter";
import { 
	getAllFieldMappings,
	getFieldMappingByApId,
	getFieldMappingByCcId,
	type SimpleFieldMapping 
} from "@/processors/patientCustomFields/fieldMappingResolver";
import { logDebug, logError, logInfo, logWarn } from "@/utils/logger";
import { apClient, ccClient } from "@/apiClient";

// ============================================================================
// TYPES
// ============================================================================

/**
 * Patient data context for value synchronization
 */
interface PatientDataContext {
	patientId: string;
	apContact?: GetAPContactType;
	ccPatient?: GetCCPatientType;
	direction: "ap_to_cc" | "cc_to_ap" | "bidirectional";
	requestId: string;
}

/**
 * Field value update context
 */
interface FieldValueUpdate {
	fieldId: string;
	fieldName: string;
	originalValue: any;
	convertedValue: any;
	conversionNotes: string[];
	platform: Platform;
}

// ============================================================================
// MAIN SYNC ENGINES
// ============================================================================

/**
 * Synchronize patient field values using context
 *
 * Main entry point for patient field value synchronization. Handles the
 * complete process of loading mappings, converting values, and updating
 * the target platform.
 *
 * @param context - Value synchronization context
 * @returns Synchronization summary
 */
export async function synchronizePatientValues(context: ValueSyncContext): Promise<SyncSummary> {
	const startTime = Date.now();
	const result: SyncSummary = {
		patientId: context.patientId,
		processedFields: 0,
		successfulUpdates: 0,
		skippedFields: 0,
		failedUpdates: 0,
		processingTimeMs: 0,
		errors: [],
		warnings: []
	};

	try {
		logInfo("Starting patient field value synchronization", {
			requestId: context.requestId,
			patientId: context.patientId,
			direction: context.direction
		});

		// Step 1: Load field mappings
		const fieldMappings = await getAllFieldMappings();
		if (fieldMappings.length === 0) {
			result.warnings.push("No field mappings found - run field definition sync first");
			result.processingTimeMs = Date.now() - startTime;
			return result;
		}

		// Step 2: Load patient data from both platforms
		const patientData = await loadPatientData({
			patientId: context.patientId,
			apContact: context.apContact,
			ccPatient: context.ccPatient,
			direction: context.direction,
			requestId: context.requestId
		});

		if (!patientData.apContact && !patientData.ccPatient) {
			result.errors.push("Failed to load patient data from either platform");
			result.processingTimeMs = Date.now() - startTime;
			return result;
		}

		// Step 3: Synchronize values based on direction
		switch (context.direction) {
			case "ap_to_cc":
				await syncApToCc(patientData, fieldMappings, result);
				break;
			case "cc_to_ap":
				await syncCcToAp(patientData, fieldMappings, result);
				break;
			case "bidirectional":
				await syncApToCc(patientData, fieldMappings, result);
				await syncCcToAp(patientData, fieldMappings, result);
				break;
		}

		result.processingTimeMs = Date.now() - startTime;

		logInfo("Patient field value synchronization completed", {
			requestId: context.requestId,
			patientId: context.patientId,
			processedFields: result.processedFields,
			successfulUpdates: result.successfulUpdates,
			skippedFields: result.skippedFields,
			failedUpdates: result.failedUpdates,
			processingTimeMs: result.processingTimeMs
		});

		return result;

	} catch (error) {
		result.errors.push(String(error));
		result.processingTimeMs = Date.now() - startTime;

		logError("Patient field value synchronization failed", {
			requestId: context.requestId,
			patientId: context.patientId,
			error: String(error),
			processingTimeMs: result.processingTimeMs
		});

		return result;
	}
}

/**
 * Synchronize patient values from AP to CC
 *
 * @param patientData - Patient data context
 * @param fieldMappings - Field mappings
 * @param result - Result object to update
 */
async function syncApToCc(
	patientData: PatientDataContext,
	fieldMappings: SimpleFieldMapping[],
	result: SyncSummary
): Promise<void> {
	if (!patientData.apContact || !patientData.ccPatient) {
		result.warnings.push("Missing patient data for AP to CC sync");
		return;
	}

	const updates: FieldValueUpdate[] = [];
	const apCustomFields = patientData.apContact.customFields || [];

	// Process each AP custom field
	for (const apField of apCustomFields) {
		try {
			result.processedFields++;

			// Find mapping for this AP field
			const mapping = fieldMappings.find(m => m.apId === apField.id);
			if (!mapping) {
				result.skippedFields++;
				logDebug("No mapping found for AP field", {
					requestId: patientData.requestId,
					apFieldId: apField.id,
					apFieldName: mapping?.apConfig?.name || "unknown"
				});
				continue;
			}

			// Skip if no value to sync
			if (!apField.value || apField.value === "") {
				result.skippedFields++;
				continue;
			}

			// Convert field type to determine conversion strategy
			const convertedType = convertFieldType(
				mapping.apConfig,
				Platform.AP,
				Platform.CC
			);

			// Convert the value
			const conversionResult = convertFieldValue(
				apField.value,
				convertedType.valueConversionType,
				{
					sourceField: mapping.apConfig,
					targetField: mapping.ccConfig,
					sourcePlatform: Platform.AP,
					targetPlatform: Platform.CC,
					requestId: patientData.requestId
				}
			);

			if (!conversionResult.success) {
				result.failedUpdates++;
				result.errors.push(`Failed to convert value for field ${mapping.apConfig.name}: ${conversionResult.notes.join(", ")}`);
				continue;
			}

			updates.push({
				fieldId: mapping.ccId,
				fieldName: mapping.ccConfig.label || mapping.ccConfig.name,
				originalValue: apField.value,
				convertedValue: conversionResult.convertedValue,
				conversionNotes: conversionResult.notes,
				platform: Platform.CC
			});

		} catch (error) {
			result.failedUpdates++;
			result.errors.push(`Error processing AP field: ${String(error)}`);
			
			logError("Failed to process AP field for CC sync", {
				requestId: patientData.requestId,
				apFieldId: apField.id,
				error: String(error)
			});
		}
	}

	// Apply updates to CC patient
	if (updates.length > 0) {
		await applyUpdatesToCcPatient(patientData.ccPatient, updates, result, patientData.requestId);
	}
}

/**
 * Synchronize patient values from CC to AP
 *
 * @param patientData - Patient data context
 * @param fieldMappings - Field mappings
 * @param result - Result object to update
 */
async function syncCcToAp(
	patientData: PatientDataContext,
	fieldMappings: SimpleFieldMapping[],
	result: SyncSummary
): Promise<void> {
	if (!patientData.ccPatient || !patientData.apContact) {
		result.warnings.push("Missing patient data for CC to AP sync");
		return;
	}

	const updates: FieldValueUpdate[] = [];
	const ccCustomFields = patientData.ccPatient.customFields || [];

	// Process each CC custom field
	for (const ccField of ccCustomFields) {
		try {
			result.processedFields++;

			// Find mapping for this CC field
			const mapping = fieldMappings.find(m => m.ccId === ccField.customField.id.toString());
			if (!mapping) {
				result.skippedFields++;
				logDebug("No mapping found for CC field", {
					requestId: patientData.requestId,
					ccFieldId: ccField.customField.id,
					ccFieldName: ccField.customField.label || ccField.customField.name
				});
				continue;
			}

			// Skip if no value to sync
			if (!ccField.value || ccField.value === "") {
				result.skippedFields++;
				continue;
			}

			// Convert field type to determine conversion strategy
			const convertedType = convertFieldType(
				mapping.ccConfig,
				Platform.CC,
				Platform.AP
			);

			// Convert the value
			const conversionResult = convertFieldValue(
				ccField.value,
				convertedType.valueConversionType,
				{
					sourceField: mapping.ccConfig,
					targetField: mapping.apConfig,
					sourcePlatform: Platform.CC,
					targetPlatform: Platform.AP,
					requestId: patientData.requestId
				}
			);

			if (!conversionResult.success) {
				result.failedUpdates++;
				result.errors.push(`Failed to convert value for field ${mapping.ccConfig.label || mapping.ccConfig.name}: ${conversionResult.notes.join(", ")}`);
				continue;
			}

			updates.push({
				fieldId: mapping.apId,
				fieldName: mapping.apConfig.name,
				originalValue: ccField.value,
				convertedValue: conversionResult.convertedValue,
				conversionNotes: conversionResult.notes,
				platform: Platform.AP
			});

		} catch (error) {
			result.failedUpdates++;
			result.errors.push(`Error processing CC field: ${String(error)}`);
			
			logError("Failed to process CC field for AP sync", {
				requestId: patientData.requestId,
				ccFieldId: ccField.customField.id,
				error: String(error)
			});
		}
	}

	// Apply updates to AP contact
	if (updates.length > 0) {
		await applyUpdatesToApContact(patientData.apContact, updates, result, patientData.requestId);
	}
}

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

/**
 * Load patient data from both platforms
 */
async function loadPatientData(context: PatientDataContext): Promise<PatientDataContext> {
	try {
		// Use provided data if available, otherwise fetch from APIs
		let apContact = context.apContact;
		let ccPatient = context.ccPatient;

		// Load AP contact if not provided
		if (!apContact && context.direction !== "cc_to_ap") {
			try {
				apContact = await apClient.contactReq.get(context.patientId);
				logDebug("Loaded AP contact data", {
					requestId: context.requestId,
					contactId: context.patientId,
					customFieldCount: apContact.customFields?.length || 0
				});
			} catch (error) {
				logWarn("Failed to load AP contact data", {
					requestId: context.requestId,
					contactId: context.patientId,
					error: String(error)
				});
			}
		}

		// Load CC patient if not provided
		if (!ccPatient && context.direction !== "ap_to_cc") {
			try {
				// Note: This assumes patientId can be used to find CC patient
				// In practice, you might need to use a mapping table
				const ccPatientId = parseInt(context.patientId);
				if (!isNaN(ccPatientId)) {
					ccPatient = await ccClient.patientReq.get(ccPatientId);
					logDebug("Loaded CC patient data", {
						requestId: context.requestId,
						patientId: ccPatientId,
						customFieldCount: ccPatient.customFields?.length || 0
					});
				}
			} catch (error) {
				logWarn("Failed to load CC patient data", {
					requestId: context.requestId,
					patientId: context.patientId,
					error: String(error)
				});
			}
		}

		return {
			...context,
			apContact,
			ccPatient
		};

	} catch (error) {
		logError("Failed to load patient data", {
			requestId: context.requestId,
			patientId: context.patientId,
			error: String(error)
		});
		return context;
	}
}

/**
 * Apply field value updates to CC patient
 */
async function applyUpdatesToCcPatient(
	ccPatient: GetCCPatientType,
	updates: FieldValueUpdate[],
	result: SyncSummary,
	requestId: string
): Promise<void> {
	try {
		// Build custom fields array for CC patient update
		const customFields = ccPatient.customFields || [];
		const updatedCustomFields = [...customFields];

		for (const update of updates) {
			try {
				// Find existing custom field or create new one
				const existingIndex = updatedCustomFields.findIndex(
					cf => cf.customField.id.toString() === update.fieldId
				);

				if (existingIndex >= 0) {
					// Update existing field
					updatedCustomFields[existingIndex] = {
						...updatedCustomFields[existingIndex],
						value: update.convertedValue
					};
				} else {
					// Add new custom field value
					updatedCustomFields.push({
						customField: { id: parseInt(update.fieldId) } as any,
						value: update.convertedValue
					});
				}

				result.successfulUpdates++;

				logDebug("Prepared CC field update", {
					requestId,
					fieldId: update.fieldId,
					fieldName: update.fieldName,
					originalValue: update.originalValue,
					convertedValue: update.convertedValue,
					conversionNotes: update.conversionNotes
				});

			} catch (error) {
				result.failedUpdates++;
				result.errors.push(`Failed to prepare update for CC field ${update.fieldName}: ${String(error)}`);
			}
		}

		// Update CC patient with new custom field values
		const updateData: Partial<PostCCPatientType> = {
			customFields: updatedCustomFields
		};

		await ccClient.patientReq.update(ccPatient.id, updateData as PostCCPatientType);

		logInfo("Successfully updated CC patient custom fields", {
			requestId,
			patientId: ccPatient.id,
			updatedFieldCount: updates.length
		});

	} catch (error) {
		result.failedUpdates += updates.length;
		result.errors.push(`Failed to update CC patient: ${String(error)}`);

		logError("Failed to update CC patient custom fields", {
			requestId,
			patientId: ccPatient.id,
			error: String(error)
		});
	}
}

/**
 * Apply field value updates to AP contact
 */
async function applyUpdatesToApContact(
	apContact: GetAPContactType,
	updates: FieldValueUpdate[],
	result: SyncSummary,
	requestId: string
): Promise<void> {
	try {
		// Build custom fields array for AP contact update
		const customFields = apContact.customFields || [];
		const updatedCustomFields = [...customFields];

		for (const update of updates) {
			try {
				// Find existing custom field or create new one
				const existingIndex = updatedCustomFields.findIndex(
					cf => cf.id === update.fieldId
				);

				if (existingIndex >= 0) {
					// Update existing field
					updatedCustomFields[existingIndex] = {
						...updatedCustomFields[existingIndex],
						value: update.convertedValue
					};
				} else {
					// Add new custom field value
					updatedCustomFields.push({
						id: update.fieldId,
						value: update.convertedValue
					});
				}

				result.successfulUpdates++;

				logDebug("Prepared AP field update", {
					requestId,
					fieldId: update.fieldId,
					fieldName: update.fieldName,
					originalValue: update.originalValue,
					convertedValue: update.convertedValue,
					conversionNotes: update.conversionNotes
				});

			} catch (error) {
				result.failedUpdates++;
				result.errors.push(`Failed to prepare update for AP field ${update.fieldName}: ${String(error)}`);
			}
		}

		// Update AP contact with new custom field values
		const updateData: Partial<APPostContactType> = {
			customFields: updatedCustomFields
		};

		await apClient.contactReq.update(apContact.id, updateData as APPostContactType);

		logInfo("Successfully updated AP contact custom fields", {
			requestId,
			contactId: apContact.id,
			updatedFieldCount: updates.length
		});

	} catch (error) {
		result.failedUpdates += updates.length;
		result.errors.push(`Failed to update AP contact: ${String(error)}`);

		logError("Failed to update AP contact custom fields", {
			requestId,
			contactId: apContact.id,
			error: String(error)
		});
	}
}
