/**
 * Field Definition Sync Engine for Custom Fields v2
 *
 * Handles field creation and mapping synchronization between AutoPatient (AP) 
 * and CliniCore (CC) platforms. This engine orchestrates the field matching,
 * type conversion, and field creation processes.
 *
 * **Key Features:**
 * - Intelligent field matching using multiple strategies
 * - Field type compatibility validation and conversion
 * - Missing field creation with proper type mapping
 * - Database mapping storage and retrieval
 * - Standard field to custom field mapping
 * - Comprehensive error handling and logging
 *
 * **Process Flow:**
 * 1. Load existing field mappings from database
 * 2. Match unmapped fields using field matcher
 * 3. Validate field type compatibility
 * 4. Create missing fields on target platforms
 * 5. Store new mappings in database
 * 6. Handle standard field mappings
 *
 * @fileoverview Field definition synchronization engine
 * @version 1.0.0
 * @since 2024-08-07
 */

import type { 
	APGetCustomFieldType, 
	GetCCCustomField,
	APPostCustomfieldType 
} from "@type";
import type {
	FieldSyncResult,
	SyncOptions,
	FieldMapping,
	FieldMatchConfig,
	Platform,
	FieldMatchStrategy,
	MappingType
} from "../types";
import { findMatchingFields } from "../core/fieldMatcher";
import { checkFieldCompatibility } from "../core/typeChecker";
import { convertFieldType } from "../core/fieldTypeConverter";
import { getStandardFieldMappings, getAllStandardFieldMappings } from "../core/standardFieldMapper";
import { 
	getAllFieldMappings, 
	saveFieldMapping,
	type SimpleFieldMapping 
} from "@/processors/patientCustomFields/fieldMappingResolver";
import { logDebug, logError, logInfo, logWarn } from "@/utils/logger";
import { apClient, ccClient } from "@/apiClient";

// ============================================================================
// TYPES
// ============================================================================

/**
 * Field creation context for new field creation
 */
interface FieldCreationContext {
	sourceField: APGetCustomFieldType | GetCCCustomField;
	targetPlatform: Platform;
	targetFieldType: string;
	requestId: string;
}

/**
 * Field matching context for field pairing
 */
interface FieldMatchingContext {
	sourceFields: (APGetCustomFieldType | GetCCCustomField)[];
	targetFields: (APGetCustomFieldType | GetCCCustomField)[];
	sourcePlatform: Platform;
	targetPlatform: Platform;
	config: FieldMatchConfig;
	requestId: string;
}

// ============================================================================
// MAIN SYNC ENGINE
// ============================================================================

/**
 * Synchronize field definitions between platforms
 *
 * Main orchestration function that handles the complete field definition
 * synchronization process including matching, creation, and mapping storage.
 *
 * @param apFields - AutoPatient custom fields
 * @param ccFields - CliniCore custom fields
 * @param options - Synchronization options
 * @returns Field synchronization result
 */
export async function synchronizeFieldDefinitions(
	apFields: APGetCustomFieldType[],
	ccFields: GetCCCustomField[],
	options: SyncOptions
): Promise<FieldSyncResult> {
	const startTime = Date.now();
	const result: FieldSyncResult = {
		success: false,
		matchedFields: [],
		createdFields: [],
		skippedFields: [],
		failedFields: [],
		processingTimeMs: 0,
		warnings: []
	};

	try {
		logInfo("Starting field definition synchronization", {
			requestId: options.requestId,
			apFieldCount: apFields.length,
			ccFieldCount: ccFields.length,
			createMissingFields: options.createMissingFields
		});

		// Step 1: Load existing field mappings from database
		const existingMappings = await getAllFieldMappings();
		logDebug("Loaded existing field mappings", {
			requestId: options.requestId,
			mappingCount: existingMappings.length
		});

		// Step 2: Identify unmapped fields
		const { unmappedApFields, unmappedCcFields } = identifyUnmappedFields(
			apFields,
			ccFields,
			existingMappings
		);

		logInfo("Identified unmapped fields", {
			requestId: options.requestId,
			unmappedApCount: unmappedApFields.length,
			unmappedCcCount: unmappedCcFields.length
		});

		// Step 3: Match AP fields to CC fields
		const apToCcMatches = await matchFields({
			sourceFields: unmappedApFields,
			targetFields: unmappedCcFields,
			sourcePlatform: Platform.AP,
			targetPlatform: Platform.CC,
			config: options.fieldMatchConfig || getDefaultMatchConfig(),
			requestId: options.requestId
		});

		// Step 4: Match CC fields to AP fields
		const ccToApMatches = await matchFields({
			sourceFields: unmappedCcFields,
			targetFields: unmappedApFields,
			sourcePlatform: Platform.CC,
			targetPlatform: Platform.AP,
			config: options.fieldMatchConfig || getDefaultMatchConfig(),
			requestId: options.requestId
		});

		// Step 5: Process matches and create mappings
		await processMatches(apToCcMatches, result, options);
		await processMatches(ccToApMatches, result, options);

		// Step 6: Handle standard field mappings if enabled
		if (options.includeStandardFields) {
			await processStandardFieldMappings(
				unmappedApFields,
				unmappedCcFields,
				result,
				options
			);
		}

		// Step 7: Create missing fields if enabled
		if (options.createMissingFields) {
			await createMissingFields(
				unmappedApFields,
				unmappedCcFields,
				result,
				options
			);
		}

		result.success = true;
		result.processingTimeMs = Date.now() - startTime;

		logInfo("Field definition synchronization completed", {
			requestId: options.requestId,
			success: result.success,
			matchedCount: result.matchedFields.length,
			createdCount: result.createdFields.length,
			skippedCount: result.skippedFields.length,
			failedCount: result.failedFields.length,
			processingTimeMs: result.processingTimeMs
		});

		return result;

	} catch (error) {
		result.success = false;
		result.processingTimeMs = Date.now() - startTime;
		result.failedFields.push({
			fieldName: "unknown",
			error: String(error),
			platform: Platform.AP
		});

		logError("Field definition synchronization failed", {
			requestId: options.requestId,
			error: String(error),
			processingTimeMs: result.processingTimeMs
		});

		return result;
	}
}

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

/**
 * Get default field matching configuration
 */
function getDefaultMatchConfig(): FieldMatchConfig {
	return {
		strategy: FieldMatchStrategy.NORMALIZED,
		fuzzyThreshold: 0.85,
		normalizeGermanChars: true,
		ignoreCase: true,
		ignoreSpaces: true
	};
}

/**
 * Identify fields that don't have existing mappings
 */
function identifyUnmappedFields(
	apFields: APGetCustomFieldType[],
	ccFields: GetCCCustomField[],
	existingMappings: SimpleFieldMapping[]
): {
	unmappedApFields: APGetCustomFieldType[];
	unmappedCcFields: GetCCCustomField[];
} {
	const mappedApIds = new Set(existingMappings.map(m => m.apId));
	const mappedCcIds = new Set(existingMappings.map(m => parseInt(m.ccId)));

	const unmappedApFields = apFields.filter(field => !mappedApIds.has(field.id));
	const unmappedCcFields = ccFields.filter(field => !mappedCcIds.has(field.id));

	return { unmappedApFields, unmappedCcFields };
}

/**
 * Match fields between platforms using the field matcher
 */
async function matchFields(context: FieldMatchingContext): Promise<Array<{
	sourceField: APGetCustomFieldType | GetCCCustomField;
	targetField: APGetCustomFieldType | GetCCCustomField;
	confidence: number;
	strategy: FieldMatchStrategy;
}>> {
	const matches: Array<{
		sourceField: APGetCustomFieldType | GetCCCustomField;
		targetField: APGetCustomFieldType | GetCCCustomField;
		confidence: number;
		strategy: FieldMatchStrategy;
	}> = [];

	for (const sourceField of context.sourceFields) {
		try {
			const fieldMatches = findMatchingFields(
				sourceField,
				context.targetFields,
				context.config,
				{
					minConfidence: 0.7,
					maxResults: 1,
					targetFieldType: undefined
				}
			);

			if (fieldMatches.length > 0) {
				const bestMatch = fieldMatches[0];
				matches.push({
					sourceField,
					targetField: bestMatch.field,
					confidence: bestMatch.confidence,
					strategy: bestMatch.matchStrategy
				});

				logDebug("Found field match", {
					requestId: context.requestId,
					sourceField: getFieldName(sourceField),
					targetField: getFieldName(bestMatch.field),
					confidence: bestMatch.confidence,
					strategy: bestMatch.matchStrategy
				});
			}
		} catch (error) {
			logWarn("Failed to match field", {
				requestId: context.requestId,
				sourceField: getFieldName(sourceField),
				error: String(error)
			});
		}
	}

	return matches;
}

/**
 * Get field name for logging
 */
function getFieldName(field: APGetCustomFieldType | GetCCCustomField): string {
	if ('name' in field && field.name) {
		return field.name;
	}
	if ('label' in field && field.label) {
		return field.label;
	}
	return `field-${field.id}`;
}

/**
 * Process field matches and create database mappings
 */
async function processMatches(
	matches: Array<{
		sourceField: APGetCustomFieldType | GetCCCustomField;
		targetField: APGetCustomFieldType | GetCCCustomField;
		confidence: number;
		strategy: FieldMatchStrategy;
	}>,
	result: FieldSyncResult,
	options: SyncOptions
): Promise<void> {
	for (const match of matches) {
		try {
			// Determine platforms and field IDs
			const isApSource = 'dataType' in match.sourceField;
			const apField = isApSource ? match.sourceField as APGetCustomFieldType : match.targetField as APGetCustomFieldType;
			const ccField = isApSource ? match.targetField as GetCCCustomField : match.sourceField as GetCCCustomField;

			// Check field compatibility
			const compatibility = checkFieldCompatibility(
				match.sourceField,
				match.targetField,
				isApSource ? Platform.AP : Platform.CC
			);

			if (!compatibility.isCompatible) {
				result.skippedFields.push({
					fieldName: getFieldName(match.sourceField),
					reason: `Incompatible field types: ${compatibility.reason}`,
					platform: isApSource ? Platform.AP : Platform.CC
				});
				continue;
			}

			// Create field mapping
			const mapping: FieldMapping = {
				id: `mapping-${apField.id}-${ccField.id}`,
				apFieldId: apField.id,
				ccFieldId: ccField.id.toString(),
				apFieldName: apField.name,
				ccFieldName: ccField.label || ccField.name,
				apFieldType: apField.dataType,
				ccFieldType: ccField.type,
				matchStrategy: match.strategy,
				confidence: match.confidence,
				isStandardField: false,
				mappingType: MappingType.CUSTOM_TO_CUSTOM,
				isActive: true
			};

			// Save mapping to database
			const saved = await saveFieldMapping({
				apId: apField.id,
				ccId: ccField.id,
				apConfig: apField,
				ccConfig: ccField,
				mappingType: "custom_to_custom"
			});

			if (saved) {
				result.matchedFields.push(mapping);
				logInfo("Created field mapping", {
					requestId: options.requestId,
					apField: apField.name,
					ccField: ccField.label || ccField.name,
					confidence: match.confidence,
					strategy: match.strategy
				});
			} else {
				result.failedFields.push({
					fieldName: getFieldName(match.sourceField),
					error: "Failed to save field mapping to database",
					platform: isApSource ? Platform.AP : Platform.CC
				});
			}

		} catch (error) {
			result.failedFields.push({
				fieldName: getFieldName(match.sourceField),
				error: String(error),
				platform: 'dataType' in match.sourceField ? Platform.AP : Platform.CC
			});

			logError("Failed to process field match", {
				requestId: options.requestId,
				sourceField: getFieldName(match.sourceField),
				targetField: getFieldName(match.targetField),
				error: String(error)
			});
		}
	}
}

/**
 * Process standard field mappings
 */
async function processStandardFieldMappings(
	unmappedApFields: APGetCustomFieldType[],
	unmappedCcFields: GetCCCustomField[],
	result: FieldSyncResult,
	options: SyncOptions
): Promise<void> {
	// Process AP fields for standard field mappings
	for (const apField of unmappedApFields) {
		try {
			const standardMappings = getStandardFieldMappings(Platform.AP, Platform.CC);
			const standardMapping = standardMappings.find(m =>
				m.targetFieldNames.some(name =>
					name.toLowerCase() === apField.name.toLowerCase()
				)
			);

			if (standardMapping) {
				logInfo("Found standard field mapping for AP field", {
					requestId: options.requestId,
					apField: apField.name,
					standardMapping: standardMapping.description
				});

				// Create mapping entry for standard field
				const mapping: FieldMapping = {
					id: `standard-${apField.id}`,
					apFieldId: apField.id,
					ccFieldId: "standard",
					apFieldName: apField.name,
					ccFieldName: standardMapping.sourceField,
					apFieldType: apField.dataType,
					ccFieldType: "standard",
					matchStrategy: FieldMatchStrategy.EXACT,
					confidence: 1.0,
					isStandardField: true,
					mappingType: MappingType.CUSTOM_TO_STANDARD,
					isActive: true
				};

				result.matchedFields.push(mapping);
			}
		} catch (error) {
			logWarn("Failed to process standard field mapping for AP field", {
				requestId: options.requestId,
				apField: apField.name,
				error: String(error)
			});
		}
	}

	// Process CC fields for standard field mappings
	for (const ccField of unmappedCcFields) {
		try {
			const fieldName = ccField.label || ccField.name;
			const standardMappings = getStandardFieldMappings(Platform.CC, Platform.AP);
			const standardMapping = standardMappings.find(m =>
				m.targetFieldNames.some(name =>
					name.toLowerCase() === fieldName.toLowerCase()
				)
			);

			if (standardMapping) {
				logInfo("Found standard field mapping for CC field", {
					requestId: options.requestId,
					ccField: fieldName,
					standardMapping: standardMapping.description
				});

				// Create mapping entry for standard field
				const mapping: FieldMapping = {
					id: `standard-${ccField.id}`,
					apFieldId: "standard",
					ccFieldId: ccField.id.toString(),
					apFieldName: standardMapping.sourceField,
					ccFieldName: fieldName,
					apFieldType: "standard",
					ccFieldType: ccField.type,
					matchStrategy: FieldMatchStrategy.EXACT,
					confidence: 1.0,
					isStandardField: true,
					mappingType: MappingType.CUSTOM_TO_STANDARD,
					isActive: true
				};

				result.matchedFields.push(mapping);
			}
		} catch (error) {
			logWarn("Failed to process standard field mapping for CC field", {
				requestId: options.requestId,
				ccField: ccField.label || ccField.name,
				error: String(error)
			});
		}
	}
}

/**
 * Create missing fields on target platforms
 */
async function createMissingFields(
	unmappedApFields: APGetCustomFieldType[],
	unmappedCcFields: GetCCCustomField[],
	result: FieldSyncResult,
	options: SyncOptions
): Promise<void> {
	// Create CC fields from unmapped AP fields
	for (const apField of unmappedApFields) {
		try {
			// Skip if this field maps to a standard field
			const standardMappings = getStandardFieldMappings(Platform.AP, Platform.CC);
			const standardMapping = standardMappings.find(m =>
				m.targetFieldNames.some(name =>
					name.toLowerCase() === apField.name.toLowerCase()
				)
			);
			if (standardMapping) {
				continue;
			}

			// Convert AP field type to CC field type
			const convertedType = convertFieldType(
				apField,
				Platform.AP,
				Platform.CC
			);

			if (!convertedType.targetType) {
				result.skippedFields.push({
					fieldName: apField.name,
					reason: `Cannot convert AP field type ${apField.dataType} to CC field type`,
					platform: Platform.AP
				});
				continue;
			}

			// Note: CC field creation is not implemented yet
			// This is a placeholder for future implementation
			result.skippedFields.push({
				fieldName: apField.name,
				reason: "CC field creation not yet implemented",
				platform: Platform.AP
			});

			logInfo("Skipped CC field creation (not implemented)", {
				requestId: options.requestId,
				apField: apField.name,
				targetType: convertedType.targetType
			});

		} catch (error) {
			result.failedFields.push({
				fieldName: apField.name,
				error: String(error),
				platform: Platform.AP
			});

			logError("Failed to create CC field", {
				requestId: options.requestId,
				apField: apField.name,
				error: String(error)
			});
		}
	}

	// Create AP fields from unmapped CC fields
	for (const ccField of unmappedCcFields) {
		try {
			const fieldName = ccField.label || ccField.name;

			// Skip if this field maps to a standard field
			const standardMappings = getStandardFieldMappings(Platform.CC, Platform.AP);
			const standardMapping = standardMappings.find(m =>
				m.targetFieldNames.some(name =>
					name.toLowerCase() === fieldName.toLowerCase()
				)
			);
			if (standardMapping) {
				continue;
			}

			// Convert CC field type to AP field type
			const convertedType = convertFieldType(
				ccField,
				Platform.CC,
				Platform.AP
			);

			if (!convertedType.targetType) {
				result.skippedFields.push({
					fieldName: fieldName,
					reason: `Cannot convert CC field type ${ccField.type} to AP field type`,
					platform: Platform.CC
				});
				continue;
			}

			// Create AP field
			const apFieldData: APPostCustomfieldType = {
				name: fieldName,
				dataType: convertedType.targetType as any,
				fieldKey: fieldName.toLowerCase().replace(/[^a-z0-9]/g, '_'),
				model: "contact"
			};

			// Add TEXTBOX_LIST options if needed
			if (convertedType.targetType === "TEXTBOX_LIST") {
				apFieldData.textBoxListOptions = ["Option 1", "Option 2", "Option 3"];
			}

			const createdField = await apClient.apCustomfield.create(apFieldData);

			// Save mapping to database
			const saved = await saveFieldMapping({
				apId: createdField.id,
				ccId: ccField.id,
				apConfig: createdField,
				ccConfig: ccField,
				mappingType: "custom_to_custom"
			});

			if (saved) {
				result.createdFields.push({
					fieldName: createdField.name,
					fieldId: createdField.id,
					platform: Platform.AP,
					fieldType: createdField.dataType
				});

				logInfo("Created AP field from CC field", {
					requestId: options.requestId,
					ccField: fieldName,
					apField: createdField.name,
					apFieldId: createdField.id,
					fieldType: createdField.dataType
				});
			} else {
				result.failedFields.push({
					fieldName: fieldName,
					error: "Failed to save field mapping after creation",
					platform: Platform.CC
				});
			}

		} catch (error) {
			result.failedFields.push({
				fieldName: ccField.label || ccField.name,
				error: String(error),
				platform: Platform.CC
			});

			logError("Failed to create AP field", {
				requestId: options.requestId,
				ccField: ccField.label || ccField.name,
				error: String(error)
			});
		}
	}
}
