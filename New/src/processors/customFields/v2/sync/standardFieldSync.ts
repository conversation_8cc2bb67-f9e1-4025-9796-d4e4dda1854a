/**
 * Standard Field Sync Engine for Custom Fields v2
 *
 * Handles synchronization between standard fields on one platform and custom 
 * fields on another platform. This engine manages the mapping and value transfer
 * for fields like email, phone, firstName that exist as standard fields on one
 * platform but need to be stored as custom fields on another.
 *
 * **Key Features:**
 * - Standard field to custom field mapping
 * - Value extraction from standard field paths
 * - Custom field creation for standard field values
 * - Bidirectional synchronization support
 * - Field type conversion and validation
 * - Comprehensive error handling and logging
 *
 * **Standard Field Mappings:**
 * - AP email/phone → CC custom fields
 * - CC patient ID → AP custom field
 * - CC profile link → AP custom field
 * - AP contact data → CC custom fields
 *
 * **Process Flow:**
 * 1. Load standard field mapping configurations
 * 2. Extract values from source platform standard fields
 * 3. Find or create target custom fields
 * 4. Convert values based on field types
 * 5. Update target platform with converted values
 * 6. Log synchronization results and errors
 *
 * @fileoverview Standard field synchronization engine
 * @version 1.0.0
 * @since 2024-08-07
 */

import type { 
	APGetCustomFieldType, 
	GetCCCustomField,
	GetAPContactType,
	GetCCPatientType,
	PostCCPatientType,
	APPostContactType
} from "@type";
import type {
	StandardFieldMapping,
	Platform,
	SyncSummary
} from "../types";
import { getStandardFieldMappings, getAllStandardFieldMappings } from "../core/standardFieldMapper";
import { convertFieldValue } from "../core/valueConverter";
import { convertFieldType } from "../core/fieldTypeConverter";
import { findMatchingFields } from "../core/fieldMatcher";
import { 
	getAllFieldMappings,
	saveFieldMapping,
	type SimpleFieldMapping 
} from "@/processors/patientCustomFields/fieldMappingResolver";
import { logDebug, logError, logInfo, logWarn } from "@/utils/logger";
import { apClient, ccClient } from "@/apiClient";

// ============================================================================
// TYPES
// ============================================================================

/**
 * Standard field sync context
 */
interface StandardFieldSyncContext {
	patientId: string;
	apContact?: GetAPContactType;
	ccPatient?: GetCCPatientType;
	direction: "ap_to_cc" | "cc_to_ap" | "bidirectional";
	requestId: string;
	createMissingFields: boolean;
}

/**
 * Standard field value extraction result
 */
interface StandardFieldValue {
	fieldPath: string;
	value: any;
	mapping: StandardFieldMapping;
	isValid: boolean;
}

/**
 * Standard field sync result
 */
interface StandardFieldSyncResult {
	processedMappings: number;
	successfulUpdates: number;
	skippedMappings: number;
	failedUpdates: number;
	createdFields: number;
	errors: string[];
	warnings: string[];
}

// ============================================================================
// MAIN SYNC ENGINE
// ============================================================================

/**
 * Synchronize standard fields to custom fields
 *
 * Main entry point for standard field synchronization. Handles the complete
 * process of mapping standard fields to custom fields and transferring values.
 *
 * @param context - Standard field sync context
 * @returns Synchronization result
 */
export async function synchronizeStandardFields(context: StandardFieldSyncContext): Promise<SyncSummary> {
	const startTime = Date.now();
	const result: SyncSummary = {
		patientId: context.patientId,
		processedFields: 0,
		successfulUpdates: 0,
		skippedFields: 0,
		failedUpdates: 0,
		processingTimeMs: 0,
		errors: [],
		warnings: []
	};

	try {
		logInfo("Starting standard field synchronization", {
			requestId: context.requestId,
			patientId: context.patientId,
			direction: context.direction,
			createMissingFields: context.createMissingFields
		});

		// Load all standard field mappings
		const standardMappings = getAllStandardFieldMappings();
		if (standardMappings.length === 0) {
			result.warnings.push("No standard field mappings configured");
			result.processingTimeMs = Date.now() - startTime;
			return result;
		}

		// Synchronize based on direction
		switch (context.direction) {
			case "ap_to_cc":
				await syncApStandardToCcCustom(context, standardMappings, result);
				break;
			case "cc_to_ap":
				await syncCcStandardToApCustom(context, standardMappings, result);
				break;
			case "bidirectional":
				await syncApStandardToCcCustom(context, standardMappings, result);
				await syncCcStandardToApCustom(context, standardMappings, result);
				break;
		}

		result.processingTimeMs = Date.now() - startTime;

		logInfo("Standard field synchronization completed", {
			requestId: context.requestId,
			patientId: context.patientId,
			processedFields: result.processedFields,
			successfulUpdates: result.successfulUpdates,
			skippedFields: result.skippedFields,
			failedUpdates: result.failedUpdates,
			processingTimeMs: result.processingTimeMs
		});

		return result;

	} catch (error) {
		result.errors.push(String(error));
		result.processingTimeMs = Date.now() - startTime;

		logError("Standard field synchronization failed", {
			requestId: context.requestId,
			patientId: context.patientId,
			error: String(error),
			processingTimeMs: result.processingTimeMs
		});

		return result;
	}
}

/**
 * Synchronize AP standard fields to CC custom fields
 */
async function syncApStandardToCcCustom(
	context: StandardFieldSyncContext,
	standardMappings: StandardFieldMapping[],
	result: SyncSummary
): Promise<void> {
	if (!context.apContact || !context.ccPatient) {
		result.warnings.push("Missing patient data for AP standard to CC custom sync");
		return;
	}

	// Filter mappings for AP → CC direction
	const apToCcMappings = standardMappings.filter(
		m => m.sourcePlatform === Platform.AP && m.targetPlatform === Platform.CC
	);

	// Get all CC custom fields for matching
	const ccCustomFields = await ccClient.ccCustomfieldReq.all();

	for (const mapping of apToCcMappings) {
		try {
			result.processedFields++;

			// Extract value from AP contact
			const standardValue = extractStandardFieldValue(
				context.apContact,
				mapping.sourceField,
				Platform.AP
			);

			if (!standardValue.isValid || !standardValue.value) {
				result.skippedFields++;
				logDebug("No value found for AP standard field", {
					requestId: context.requestId,
					fieldPath: mapping.sourceField,
					mapping: mapping.description
				});
				continue;
			}

			// Find or create matching CC custom field
			const targetField = await findOrCreateTargetField(
				mapping,
				ccCustomFields,
				context.createMissingFields,
				Platform.CC,
				context.requestId
			);

			if (!targetField) {
				result.skippedFields++;
				result.warnings.push(`No target field found for mapping: ${mapping.description}`);
				continue;
			}

			// Convert value for CC custom field
			const convertedType = convertFieldType(
				{ name: mapping.sourceField, dataType: "TEXT" } as any,
				Platform.AP,
				Platform.CC
			);

			const conversionResult = convertFieldValue(
				standardValue.value,
				convertedType.valueConversionType,
				{
					sourceField: { name: mapping.sourceField } as any,
					targetField: targetField,
					sourcePlatform: Platform.AP,
					targetPlatform: Platform.CC,
					requestId: context.requestId
				}
			);

			if (!conversionResult.success) {
				result.failedUpdates++;
				result.errors.push(`Failed to convert value for ${mapping.description}: ${conversionResult.notes.join(", ")}`);
				continue;
			}

			// Update CC patient custom field
			await updateCcCustomField(
				context.ccPatient,
				targetField,
				conversionResult.convertedValue,
				context.requestId
			);

			result.successfulUpdates++;

			logInfo("Synchronized AP standard field to CC custom field", {
				requestId: context.requestId,
				sourceField: mapping.sourceField,
				targetField: targetField.label || targetField.name,
				value: conversionResult.convertedValue
			});

		} catch (error) {
			result.failedUpdates++;
			result.errors.push(`Error syncing ${mapping.description}: ${String(error)}`);
			
			logError("Failed to sync AP standard field to CC custom field", {
				requestId: context.requestId,
				mapping: mapping.description,
				error: String(error)
			});
		}
	}
}

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

/**
 * Extract value from standard field path
 */
function extractStandardFieldValue(
	data: GetAPContactType | GetCCPatientType,
	fieldPath: string,
	platform: Platform
): StandardFieldValue {
	try {
		// Parse field path (e.g., "patient.email", "contact.phone")
		const pathParts = fieldPath.split('.');
		let value = data as any;

		// Navigate through the object path
		for (const part of pathParts) {
			if (value && typeof value === 'object' && part in value) {
				value = value[part];
			} else {
				return {
					fieldPath,
					value: null,
					mapping: {} as StandardFieldMapping,
					isValid: false
				};
			}
		}

		// Validate that we have a meaningful value
		const isValid = value !== null && value !== undefined && value !== "";

		return {
			fieldPath,
			value,
			mapping: {} as StandardFieldMapping,
			isValid
		};

	} catch (error) {
		logWarn("Failed to extract standard field value", {
			fieldPath,
			platform,
			error: String(error)
		});

		return {
			fieldPath,
			value: null,
			mapping: {} as StandardFieldMapping,
			isValid: false
		};
	}
}

/**
 * Find or create target custom field for standard field mapping
 */
async function findOrCreateTargetField(
	mapping: StandardFieldMapping,
	customFields: (APGetCustomFieldType | GetCCCustomField)[],
	createMissingFields: boolean,
	targetPlatform: Platform,
	requestId: string
): Promise<APGetCustomFieldType | GetCCCustomField | null> {
	try {
		// Try to find existing field by matching names
		for (const targetName of mapping.targetFieldNames) {
			const matches = findMatchingFields(
				{ name: targetName } as any,
				customFields,
				{
					strategy: "normalized" as any,
					fuzzyThreshold: 0.8,
					normalizeGermanChars: true,
					ignoreCase: true,
					ignoreSpaces: true
				},
				{
					minConfidence: 0.8,
					maxResults: 1,
					targetFieldType: mapping.targetFieldType
				}
			);

			if (matches.length > 0) {
				logDebug("Found existing target field for standard mapping", {
					requestId,
					targetName,
					foundField: getFieldName(matches[0].field),
					confidence: matches[0].confidence
				});
				return matches[0].field;
			}
		}

		// Create new field if enabled and no match found
		if (createMissingFields && targetPlatform === Platform.AP) {
			const newField = await createApCustomField(mapping, requestId);
			if (newField) {
				logInfo("Created new AP custom field for standard mapping", {
					requestId,
					mapping: mapping.description,
					fieldName: newField.name,
					fieldId: newField.id
				});
				return newField;
			}
		}

		// CC field creation not implemented yet
		if (createMissingFields && targetPlatform === Platform.CC) {
			logWarn("CC field creation not implemented for standard mapping", {
				requestId,
				mapping: mapping.description
			});
		}

		return null;

	} catch (error) {
		logError("Failed to find or create target field", {
			requestId,
			mapping: mapping.description,
			targetPlatform,
			error: String(error)
		});
		return null;
	}
}

/**
 * Create new AP custom field for standard field mapping
 */
async function createApCustomField(
	mapping: StandardFieldMapping,
	requestId: string
): Promise<APGetCustomFieldType | null> {
	try {
		const fieldData = {
			name: mapping.targetFieldNames[0],
			dataType: mapping.targetFieldType as any,
			fieldKey: mapping.targetFieldNames[0].toLowerCase().replace(/[^a-z0-9]/g, '_'),
			model: "contact"
		};

		// Add TEXTBOX_LIST options if needed
		if (mapping.targetFieldType === "TEXTBOX_LIST") {
			(fieldData as any).textBoxListOptions = ["Option 1", "Option 2", "Option 3"];
		}

		const createdField = await apClient.apCustomfield.create(fieldData);

		logInfo("Created AP custom field for standard mapping", {
			requestId,
			fieldName: createdField.name,
			fieldId: createdField.id,
			fieldType: createdField.dataType,
			mapping: mapping.description
		});

		return createdField;

	} catch (error) {
		logError("Failed to create AP custom field for standard mapping", {
			requestId,
			mapping: mapping.description,
			error: String(error)
		});
		return null;
	}
}

/**
 * Update CC patient custom field
 */
async function updateCcCustomField(
	ccPatient: GetCCPatientType,
	targetField: GetCCCustomField,
	value: any,
	requestId: string
): Promise<void> {
	try {
		// Build custom fields array for update
		const customFields = ccPatient.customFields || [];
		const updatedCustomFields = [...customFields];

		// Find existing field or add new one
		const existingIndex = updatedCustomFields.findIndex(
			cf => cf.customField.id === targetField.id
		);

		if (existingIndex >= 0) {
			updatedCustomFields[existingIndex] = {
				...updatedCustomFields[existingIndex],
				value: value
			};
		} else {
			updatedCustomFields.push({
				customField: targetField,
				value: value
			});
		}

		// Update CC patient
		const updateData: Partial<PostCCPatientType> = {
			customFields: updatedCustomFields
		};

		await ccClient.patientReq.update(ccPatient.id, updateData as PostCCPatientType);

		logDebug("Updated CC patient custom field", {
			requestId,
			patientId: ccPatient.id,
			fieldName: targetField.label || targetField.name,
			value: value
		});

	} catch (error) {
		logError("Failed to update CC patient custom field", {
			requestId,
			patientId: ccPatient.id,
			fieldName: targetField.label || targetField.name,
			error: String(error)
		});
		throw error;
	}
}

/**
 * Update AP contact custom field
 */
async function updateApCustomField(
	apContact: GetAPContactType,
	targetField: APGetCustomFieldType,
	value: any,
	requestId: string
): Promise<void> {
	try {
		// Build custom fields array for update
		const customFields = apContact.customFields || [];
		const updatedCustomFields = [...customFields];

		// Find existing field or add new one
		const existingIndex = updatedCustomFields.findIndex(
			cf => cf.id === targetField.id
		);

		if (existingIndex >= 0) {
			updatedCustomFields[existingIndex] = {
				...updatedCustomFields[existingIndex],
				value: value
			};
		} else {
			updatedCustomFields.push({
				id: targetField.id,
				value: value
			});
		}

		// Update AP contact
		const updateData: Partial<APPostContactType> = {
			customFields: updatedCustomFields
		};

		await apClient.contactReq.update(apContact.id, updateData as APPostContactType);

		logDebug("Updated AP contact custom field", {
			requestId,
			contactId: apContact.id,
			fieldName: targetField.name,
			value: value
		});

	} catch (error) {
		logError("Failed to update AP contact custom field", {
			requestId,
			contactId: apContact.id,
			fieldName: targetField.name,
			error: String(error)
		});
		throw error;
	}
}

/**
 * Get field name for logging
 */
function getFieldName(field: APGetCustomFieldType | GetCCCustomField): string {
	if ('name' in field && field.name) {
		return field.name;
	}
	if ('label' in field && field.label) {
		return field.label;
	}
	return `field-${field.id}`;
}

/**
 * Synchronize CC standard fields to AP custom fields
 */
async function syncCcStandardToApCustom(
	context: StandardFieldSyncContext,
	standardMappings: StandardFieldMapping[],
	result: SyncSummary
): Promise<void> {
	if (!context.ccPatient || !context.apContact) {
		result.warnings.push("Missing patient data for CC standard to AP custom sync");
		return;
	}

	// Filter mappings for CC → AP direction
	const ccToApMappings = standardMappings.filter(
		m => m.sourcePlatform === Platform.CC && m.targetPlatform === Platform.AP
	);

	// Get all AP custom fields for matching
	const apCustomFields = await apClient.apCustomfield.allWithParentFilter();

	for (const mapping of ccToApMappings) {
		try {
			result.processedFields++;

			// Extract value from CC patient
			const standardValue = extractStandardFieldValue(
				context.ccPatient,
				mapping.sourceField,
				Platform.CC
			);

			if (!standardValue.isValid || !standardValue.value) {
				result.skippedFields++;
				logDebug("No value found for CC standard field", {
					requestId: context.requestId,
					fieldPath: mapping.sourceField,
					mapping: mapping.description
				});
				continue;
			}

			// Find or create matching AP custom field
			const targetField = await findOrCreateTargetField(
				mapping,
				apCustomFields,
				context.createMissingFields,
				Platform.AP,
				context.requestId
			);

			if (!targetField) {
				result.skippedFields++;
				result.warnings.push(`No target field found for mapping: ${mapping.description}`);
				continue;
			}

			// Convert value for AP custom field
			const convertedType = convertFieldType(
				{ name: mapping.sourceField, type: "text" } as any,
				Platform.CC,
				Platform.AP
			);

			const conversionResult = convertFieldValue(
				standardValue.value,
				convertedType.valueConversionType,
				{
					sourceField: { name: mapping.sourceField } as any,
					targetField: targetField,
					sourcePlatform: Platform.CC,
					targetPlatform: Platform.AP,
					requestId: context.requestId
				}
			);

			if (!conversionResult.success) {
				result.failedUpdates++;
				result.errors.push(`Failed to convert value for ${mapping.description}: ${conversionResult.notes.join(", ")}`);
				continue;
			}

			// Update AP contact custom field
			await updateApCustomField(
				context.apContact,
				targetField,
				conversionResult.convertedValue,
				context.requestId
			);

			result.successfulUpdates++;

			logInfo("Synchronized CC standard field to AP custom field", {
				requestId: context.requestId,
				sourceField: mapping.sourceField,
				targetField: targetField.name,
				value: conversionResult.convertedValue
			});

		} catch (error) {
			result.failedUpdates++;
			result.errors.push(`Error syncing ${mapping.description}: ${String(error)}`);
			
			logError("Failed to sync CC standard field to AP custom field", {
				requestId: context.requestId,
				mapping: mapping.description,
				error: String(error)
			});
		}
	}
}
