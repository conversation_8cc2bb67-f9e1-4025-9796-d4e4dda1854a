/**
 * Sync Engines Index for Custom Fields v2
 *
 * Central export point for all synchronization engines in the Custom Fields v2 system.
 * Provides easy access to field definition sync, field value sync, and standard field sync.
 *
 * **Available Sync Engines:**
 * - Field Definition Sync: Handles field creation and mapping between platforms
 * - Field Value Sync: Manages patient field value synchronization
 * - Standard Field Sync: Handles standard field to custom field mapping
 *
 * **Usage:**
 * ```typescript
 * import { 
 *   synchronizeFieldDefinitions,
 *   synchronizePatientValues,
 *   synchronizeStandardFields
 * } from './sync';
 *
 * // Synchronize field definitions
 * const fieldResult = await synchronizeFieldDefinitions(apFields, ccFields, options);
 *
 * // Synchronize patient values
 * const valueResult = await synchronizePatientValues(context);
 *
 * // Synchronize standard fields
 * const standardResult = await synchronizeStandardFields(context);
 * ```
 *
 * @fileoverview Sync engines index
 * @version 1.0.0
 * @since 2024-08-07
 */

// Field Definition Sync Engine
export { synchronizeFieldDefinitions } from "./fieldDefinitionSync";

// Field Value Sync Engine
export { synchronizePatientValues } from "./fieldValueSync";

// Standard Field Sync Engine
export { synchronizeStandardFields } from "./standardFieldSync";

// Re-export types for convenience
export type {
	FieldSyncResult,
	ValueSyncResult,
	ValueSyncContext,
	SyncSummary,
	SyncOptions
} from "../types";
