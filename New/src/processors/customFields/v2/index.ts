/**
 * Custom Fields v2 Main Entry Point
 * 
 * Main entry point for the v2 custom field synchronization system.
 * Provides the primary classes and interfaces that handlers expect.
 * 
 * @fileoverview Main entry point for custom fields v2 system
 * @version 2.0.0
 * @since 2024-08-07
 */

import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import type {
	FieldMatchConfig,
	FieldSyncResult,
	ValueSyncResult,
	ValueSyncContext,
	SyncSummary,
	SyncOptions,
	PatientData
} from "./types";

// Re-export types and enums for easy access
export {
	FieldMatchStrategy,
	Platform,
	MappingType,
	type FieldMatchConfig,
	type FieldSyncResult,
	type ValueSyncResult,
	type ValueSyncContext,
	type SyncSummary,
	type SyncOptions,
	type FieldMapping,
	type PatientData,
	type StandardFieldMapping,
	type CompatibilityResult,
	type ConversionResult,
	type ConvertedFieldType,
	type FieldMatch,
	type FieldMatchOptions,
	type ValueConversionContext
} from "./types";

// Re-export core functionality
export {
	convertFieldType,
	getApFieldTypeForCcField,
	getCcFieldTypeForApField,
	isMultiValueField,
	isMedicalField,
	isApMultiValueType,
	isApBooleanRadio,
	findMatchingFields,
	calculateMatchConfidence,
	convertFieldValue,
	convertCcMultiValueToTextboxList,
	convertTextboxListToCcMultiValue,
	convertCcMultiValueToText,
	convertTextToCcMultiValue,
	convertCcBooleanToRadio,
	convertRadioToCcBoolean,
	getBooleanRadioOptions,
	getMultiValueTextSeparator,
	checkFieldCompatibility,
	supportsMultiValue,
	getCompatibleFieldTypes,
	requiresValueConversion,
	findMatchingCustomField,
	getStandardFieldMappings,
	convertStandardFieldValue,
	extractStandardFieldValues,
	createMissingStandardFields,
	validateStandardFieldMapping,
	getStandardFieldMappingById,
	getAllStandardFieldMappings
} from "./core";

// ============================================================================
// MAIN CUSTOM FIELD SYNC CLASS
// ============================================================================

/**
 * Main Custom Field Synchronization Class (v2)
 * 
 * Primary orchestrator for custom field synchronization between AutoPatient
 * and CliniCore platforms. Handles field definition sync and patient value sync.
 */
export class CustomFieldSyncV2 {
	private config: FieldMatchConfig;

	/**
	 * Initialize the custom field sync system
	 * 
	 * @param config - Field matching configuration
	 */
	constructor(config?: FieldMatchConfig) {
		this.config = config || {
			strategy: FieldMatchStrategy.NORMALIZED,
			fuzzyThreshold: 0.85,
			normalizeGermanChars: true,
			ignoreCase: true,
			ignoreSpaces: true
		};
	}

	/**
	 * Synchronize field definitions between platforms
	 *
	 * Matches fields between AP and CC, creates missing fields, and stores
	 * mappings in the database for future value synchronization.
	 *
	 * @param apFields - AutoPatient custom fields
	 * @param ccFields - CliniCore custom fields
	 * @param options - Synchronization options
	 * @returns Field synchronization result
	 */
	async synchronizeFields(
		apFields: APGetCustomFieldType[],
		ccFields: GetCCCustomField[],
		options: SyncOptions
	): Promise<FieldSyncResult> {
		// Import the sync engine
		const { synchronizeFieldDefinitions } = await import("./sync/fieldDefinitionSync");

		// Add field match config to options
		const syncOptions = {
			...options,
			fieldMatchConfig: this.config
		};

		// Delegate to the field definition sync engine
		return await synchronizeFieldDefinitions(apFields, ccFields, syncOptions);
	}

	/**
	 * Synchronize patient values between platforms
	 *
	 * Takes patient data and field mappings, converts values appropriately,
	 * and updates the target platform with synchronized field values.
	 *
	 * @param patientData - Patient data to synchronize
	 * @param options - Synchronization options
	 * @returns Value synchronization result
	 */
	async synchronizePatientValues(
		patientData: PatientData,
		options: SyncOptions
	): Promise<ValueSyncResult> {
		// Import the sync engine
		const { synchronizePatientValues } = await import("./sync/fieldValueSync");

		// Create value sync context
		const context: ValueSyncContext = {
			patientId: patientData.id,
			apContact: patientData.apContact,
			ccPatient: patientData.ccPatient,
			direction: options.direction || "bidirectional",
			requestId: options.requestId
		};

		// Delegate to the field value sync engine
		const result = await synchronizePatientValues(context);

		// Convert SyncSummary to ValueSyncResult
		return {
			success: result.errors.length === 0,
			patientId: result.patientId,
			processedFields: result.processedFields,
			successfulUpdates: result.successfulUpdates,
			skippedFields: result.skippedFields,
			failedUpdates: result.failedUpdates,
			processingTimeMs: result.processingTimeMs,
			errors: result.errors,
			warnings: result.warnings
		};
	}
}

// ============================================================================
// FIELD VALUE SYNC CLASS
// ============================================================================

/**
 * Field Value Synchronization Class
 *
 * Specialized class for patient field value synchronization used by
 * admin endpoints and direct sync operations.
 */
export class FieldValueSync {
	/**
	 * Synchronize patient values using context
	 *
	 * @param context - Value synchronization context
	 * @returns Synchronization summary
	 */
	async synchronizePatientValues(context: ValueSyncContext): Promise<SyncSummary> {
		// Import the sync engine
		const { synchronizePatientValues } = await import("./sync/fieldValueSync");

		// Delegate to the field value sync engine
		return await synchronizePatientValues(context);
	}
}

// ============================================================================
// CONVENIENCE EXPORTS
// ============================================================================

// Import FieldMatchStrategy enum for re-export
import { FieldMatchStrategy } from "./types";

// Default export for easy importing
export default CustomFieldSyncV2;
